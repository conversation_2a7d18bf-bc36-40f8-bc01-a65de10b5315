using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Identity;

namespace CinemaBooking.Models
{
    [Table("nguoi_dung")]
    public class NguoiDung : IdentityUser<int>
    {
        [Column("ma_nguoi_dung")]
        public override int Id { get; set; }

        [Required]
        [Column("ten_dang_nhap")]
        [StringLength(50)]
        public override string UserName { get; set; } = string.Empty;

        [Required]
        [Column("email")]
        [StringLength(100)]
        [EmailAddress]
        public override string Email { get; set; } = string.Empty;

        [Column("ho_ten")]
        [StringLength(100)]
        public string? HoTen { get; set; }

        [Column("so_dien_thoai")]
        [StringLength(15)]
        public override string? PhoneNumber { get; set; }

        [Column("ngay_tao")]
        public DateTime? NgayTao { get; set; }

        [Column("ma_vai_tro")]
        public int? MaVaiTro { get; set; }

        [ForeignKey("MaVaiTro")]
        public virtual VaiTro? VaiTro { get; set; }

        public virtual ICollection<DanhGia> DanhGias { get; set; } = new List<DanhGia>();
        public virtual ICollection<DatVe> DatVes { get; set; } = new List<DatVe>();
        public virtual ICollection<LichSuGiaoDich> LichSuGiaoDichs { get; set; } = new List<LichSuGiaoDich>();
    }
} 